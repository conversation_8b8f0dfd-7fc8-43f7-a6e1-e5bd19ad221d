/* Variables */

// Base color
$mine:#FFC200;
$loginBg: #FFCFAC;
$blue:#2892E5;
$green: #1DC779;
$yellow:#FFB302;
$pink: #F56C6C;
$block:#000;
$gray-1: #20232A;
$gray-2: #818693;
$gray-3: #BAC0CD;
$gray-4: #D8DDE3;
$gray-5: #F3F4F7;
$gray-6: #EBEEF5;
$bgColor: #FAFAFB;
$white: #fff;

// Sidebar
$sideBarWidth: 190px;
$subMenuBg:#272A36;
$subMenuHover:#272A36;
$subMenuActiveText:#f4f4f5;
$menuBg:#343744;
$menuText:#bfcbd9;
$menuActiveText:$mine; //Also see settings.sidebarTextTheme

// Login page
$lightGray: #080808;
$darkGray:#889aa4;
$loginCursorColor: #080808;

// The :export directive is the magic sauce for webpack
// https://mattferderer.com/use-sass-variables-in-typescript-and-javascript
:export {
  menuBg: $menuBg;
  menuText: $menuText;
  menuActiveText: $menuActiveText;
}

.avatar-uploader .el-upload{
  // border: dashed 2px #D8DDE3 !important;
  border-radius: 4px !important;
  background: #FCFCFC;
}
.avatar-uploader .avatar-uploader-icon{
  background: #FCFCFC;
}
.avatar-uploader .el-icon-plus:before{
  content: '上传图片' !important;
  font-size: 12px;
  color:#000;
}


// 弹框样式处理
.el-dialog{
  border-radius: 8px;
}
.el-dialog__header{
  background: #FBFBFA;
  border-radius: 8px 8px 0 0;
  border: none;
}
